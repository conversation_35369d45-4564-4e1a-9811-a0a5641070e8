// swagger.js
import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: 'API for managing store credits, gift cards, and newsletter subscriptions',
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: 'https://telephone-antonio-dubai-replacing.trycloudflare.com',
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ]
};

const options = {
  swaggerDefinition,
  apis: [
    './web/swagger.js',                        // This file for basic docs
    './web/docs/swagger-definitions.js',       // Schema definitions
    './web/docs/newsletterRouter.swagger.js',  // Newsletter API docs
    './web/docs/storeCreditRouter.swagger.js', // Store Credit API docs
    './web/routes/*.js'                        // Route files for any inline docs
  ],
};

/**
 * @swagger
 * /api/healthcheck:
 *   get:
 *     tags: [System]
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "HealthCheck - OK with EB!!!"

 */

const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
