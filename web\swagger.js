// swagger.js
import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'My API Docs',
    version: '1.0.0',
    description: 'Auto-generated Swagger docs for MiniKlub Store Credit App',
  },
  servers: [
    {
      url: 'http://localhost:3000/api', // Adjust base URL
    },
  ],
};

const options = {
  swaggerDefinition,
  apis: ['./routes/*.js'], // Path to the API docs (where you use Swagger comments)
};

const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
