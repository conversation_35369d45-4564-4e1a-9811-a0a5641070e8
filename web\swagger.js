// swagger.js
import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: `
# MiniKlub Store Credit App API Documentation

This API provides comprehensive store credit and newsletter management functionality for the MiniKlub e-commerce platform.

## Features

### Store Credit Management
- **QwikCilver Integration**: Gift card balance checking and redemption
- **Rewardify Integration**: Store credit management and customer account crediting
- **Transaction Management**: Secure transaction processing with unique IDs

### Newsletter Management
- **Subscription Management**: Subscribe/unsubscribe customers from newsletters
- **Shopify Integration**: Direct integration with Shopify customer API
- **Email Marketing Consent**: Manage customer email marketing preferences

## Authentication

The API uses different authentication methods for different services:
- **QwikCilver**: Bearer token obtained from \`/api/store-credit/auth\`
- **Rewardify**: Bearer token obtained from \`/api/store-credit/rewardify-auth\`
- **Newsletter**: Internal Shopify authentication (handled automatically)

## Error Handling

All endpoints return standardized error responses with:
- HTTP status codes
- Error codes and messages
- Field-specific validation errors
- Stack traces (development only)

## Rate Limiting

Please be mindful of API rate limits:
- QwikCilver: Follow their API guidelines
- Rewardify: Standard OAuth2 rate limits apply
- Shopify: Subject to Shopify API rate limits
    `,
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    },
    license: {
      name: 'Private License',
      url: 'https://miniklub.com/license'
    }
  },
  servers: [
    {
      url: 'https://august-enclosure-heating-photograph.trycloudflare.com',
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ],
  externalDocs: {
    description: 'Find more info about MiniKlub',
    url: 'https://miniklub.com/docs'
  }
};

const options = {
  swaggerDefinition,
  apis: [
    './web/docs/*.js',           // Include all documentation files
    './web/routes/*.js',         // Include route files for any inline docs
    './web/controllers/*.js',    // Include controller files for any inline docs
    './web/swagger.js'           // Include this file for any inline docs
  ],
};

/**
 * @swagger
 * /api/healthcheck:
 *   get:
 *     tags: [System]
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "HealthCheck - OK with EB!!!"
 */

const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
