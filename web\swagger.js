// swagger.js
import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'MiniKlub Store Credit App API',
    version: '1.0.0',
    description: 'API for managing store credits, gift cards, and newsletter subscriptions',
    contact: {
      name: 'MiniKlub Development Team',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: 'https://telephone-antonio-dubai-replacing.trycloudflare.com',
      description: 'Development server'
    },
    {
      url: 'http://localhost:3000',
      description: 'Local development server'
    }
  ]
};

const options = {
  swaggerDefinition,
  apis: [
    './web/swagger.js'  // Start with just this file to test
  ],
};

/**
 * @swagger
 * /api/healthcheck:
 *   get:
 *     tags: [System]
 *     summary: Health check endpoint
 *     description: Returns the health status of the API
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "HealthCheck - OK with EB!!!"
 *
 * /api/store-credit/auth:
 *   post:
 *     tags: [Store Credit]
 *     summary: Authenticate with QwikCilver
 *     description: Get authentication token for QwikCilver API
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - transactionId
 *             properties:
 *               transactionId:
 *                 type: string
 *                 example: "123456789"
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 responseCode:
 *                   type: integer
 *                   example: 0
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     authToken:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *
 * /api/store-credit/balance:
 *   post:
 *     tags: [Store Credit]
 *     summary: Check gift card balance
 *     description: Check the balance of a gift card
 *     parameters:
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *           example: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *       - in: header
 *         name: TransactionId
 *         required: true
 *         schema:
 *           type: string
 *           example: "123456789"
 *       - in: header
 *         name: DateAtClient
 *         required: true
 *         schema:
 *           type: string
 *           example: "2025-06-24T10:57:37.000Z"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               Cards:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     CardNumber:
 *                       type: string
 *                       example: "1006507003998768"
 *                     CardPIN:
 *                       type: string
 *                       example: "143048"
 *     responses:
 *       200:
 *         description: Balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 responseCode:
 *                   type: integer
 *                   example: 0
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     balance:
 *                       type: number
 *                       example: 1000
 *                     cardStatus:
 *                       type: string
 *                       example: "Activated"
 *
 * /api/newsletter/subscribe:
 *   post:
 *     tags: [Newsletter]
 *     summary: Subscribe or unsubscribe from newsletter
 *     description: Manage newsletter subscription status
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - action
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               action:
 *                 type: string
 *                 enum: [subscribe, unsubscribe]
 *                 example: "subscribe"
 *     responses:
 *       200:
 *         description: Subscription updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 responseCode:
 *                   type: integer
 *                   example: 0
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       example: "Successfully subscribed to newsletter"
 */

const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
