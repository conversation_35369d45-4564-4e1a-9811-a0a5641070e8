import express from "express";
import { creditStoreCreditToCustomer, fetchRewardifyTransactions, fetchRewardifyOrder } from "../controllers/rewardifyController.js";
const router = express.Router();
router.get("/order/:orderId", fetchRewardifyOrder); 
router.get("/order/:orderId/transactions", fetchRewardifyTransactions); 
router.put("/customer/:shopifyCustomerId", creditStoreCreditToCustomer);

export default router;
