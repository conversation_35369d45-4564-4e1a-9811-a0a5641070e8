import express from "express";
import { creditStoreCreditToCustomer, fetchRewardifyTransactions, fetchRewardifyOrder } from "../controllers/rewardifyController.js";


const router = express.Router();
router.get("/", (req, res) => {
  console.log("Welcome to Rewardify");
  res.send("Rewardify route is working");
});

// Order routes
router.get("/order/:orderId", fetchRewardifyOrder);
router.get("/order/:orderId/transactions", fetchRewardifyTransactions);

// Customer routes
router.put("/customer/:shopifyCustomerId", creditStoreCreditToCustomer);

export default router;
