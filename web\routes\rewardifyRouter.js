import express from "express";
import { creditStoreCreditToCustomer, fetchRewardifyTransactions, fetchRewardifyOrder, testShopifyAuth, testProportionalRefund } from "../controllers/rewardifyController.js";


const router = express.Router();
router.get("/", (req, res) => {
  console.log("Welcome to Rewardify");
  res.send("Rewardify route is working");
});

// Test route
router.get("/test-auth", testShopifyAuth); // Test Shopify authentication

// Order routes
router.get("/order/:orderId", fetchRewardifyOrder); // Fetches Shopify order details
router.get("/order/:orderId/transactions", fetchRewardifyTransactions); // Fetches Rewardify transactions

// Customer routes
router.put("/customer/:shopifyCustomerId", creditStoreCreditToCustomer);

// Test route for proportional refund
router.post("/test/proportional-refund", testProportionalRefund);

export default router;
