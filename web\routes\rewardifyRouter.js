import express from "express";
import { creditStoreCreditToCustomer, fetchRewardifyTransactions } from "../controllers/rewardifyController.js";


const router = express.Router();
router.get("/", (req, res) => {
  console.log("Welcome to Rewardify");
  res.send("Rewardify route is working");
});
router.get("/order/:orderId/transactions", fetchRewardifyTransactions);
router.put("/customer/:shopifyCustomerId", creditStoreCreditToCustomer);

export default router;
