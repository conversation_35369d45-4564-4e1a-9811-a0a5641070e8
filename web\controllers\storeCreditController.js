import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { authorizeQwikCilver, getGiftCardBalance, redeemGiftCard, rewardifyAuth, creditRewardifyStoreCredit } from "../services/storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";

export const qwikcilverAuth = catchAsync(async (req, res) => {
  // console.log("qwikcilverAuth called");
  // const storeData = await getATFromSQL();
  // console.log("storeDta", storeData);
  // const shop = process.env.SHOP;
  // console.log("shop", shop);
  // const AT = storeData.find((x) => x.shop === shop)?.accessToken;
  // if (!AT) {
  //   console.log("No access token found for shop", shop);
  //   throw new AppError("Authentication failed", 401, {
  //     errors: [
  //       {
  //         field: "auth",
  //         message: "Access token not found for the specified shop.",
  //       },
  //     ],
  //   });
  // }
  const { transactionId } = req.body;
  console.log("Transactio Id", transactionId);
  if (!transactionId) {
    console.log("No transactionId provided in body");
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "body",
          message: "transactionId is required in request body",
        },
      ],
    });
  }
  const result = await authorizeQwikCilver(transactionId);
  console.log("QwikCilver Auth result", result);
  return new AppSuccess(res, { authToken: result.AuthToken });
});

export const getGiftCardBalanceController = catchAsync(async (req, res) => {
  const { authorization, transactionid, dateatclient } = req.headers;
  const { Cards } = req.body;

  const authToken = authorization?.replace("Bearer ", "");
  const transactionId = transactionid;
  const dateAtClient = dateatclient;

  if (!authToken || !transactionId || !dateAtClient || !Cards || !Cards[0]?.CardNumber || !Cards[0]?.CardPIN) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "headers/body",
          message:
            "Authorization, TransactionId, DateAtClient (headers), and Cards[0] with CardNumber & CardPIN (body) are required",
        },
      ],
    });
  }

  const { CardNumber: cardNumber, CardPIN: cardPIN } = Cards[0];

  try {
    const result = await getGiftCardBalance({
      authToken,
      transactionId,
      cardNumber,
      cardPIN,
    });
    const card = result.Cards[0];

    return new AppSuccess(res, {
      balance: card.Balance,
      cardStatus: card.CardStatus,
    });
  } catch (error) {
    const errRes = error.response?.data || error;
    throw new AppError("Failed to fetch gift card balance", error.response?.status || 500, {
      errors: [
        {
          field: "qwikcilver",
          message: errRes.message || JSON.stringify(errRes),
        },
      ],
    });
  }
});

export const redeemGiftCardController = catchAsync(async (req, res) => {
  const { authorization, transactionid, dateatclient } = req.headers;
  const { Cards } = req.body;

  const authToken = authorization?.replace("Bearer ", "");
  const balanceCheckTransactionId = transactionid;
  const dateAtClient = dateatclient;

  if (!authToken || !balanceCheckTransactionId || !dateAtClient) {
    throw new AppError("Missing headers", 400, {
      errors: [
        { field: "headers", message: "Authorization, TransactionId, and DateAtClient are required" }
      ]
    });
  }

  if (!Cards || !Cards[0]?.CardNumber || !Cards[0]?.CardPin || !Cards[0]?.Amount || !Cards[0]?.InvoiceAmount) {
    throw new AppError("Missing card data", 400, {
      errors: [
        {
          field: "body.Cards",
          message: "Cards[0] with CardNumber, CardPin, Amount, and InvoiceAmount is required"
        }
      ]
    });
  }

  const { CardNumber, CardPin, Amount, InvoiceAmount } = Cards[0];

  const invoiceNumber = `INV-${Date.now()}`;
  const idempotencyKey = `${invoiceNumber}-${generateIdempotencyKey()}`;
  const redeemTransactionId = generateStringTransactionId();
  const notes = `{VldType~GCRDM|AMT~${Amount}|BillAmount~${InvoiceAmount}|InvoiceNumber~${invoiceNumber}}`;

  const balanceResult = await getGiftCardBalance({
    authToken,
    transactionId: balanceCheckTransactionId,
    cardNumber: CardNumber,
    cardPIN: CardPin,
  });

  if (!balanceResult.Cards || balanceResult.Cards.length === 0) {
    throw new AppError("Card not found or invalid", 404);
  }

  const payload = {
    TransactionTypeId: 302,
    InputType: 1,
    InvoiceNumber: invoiceNumber,
    IdempotencyKey: idempotencyKey,
    Cards: [
      {
        CardNumber,
        CardPin,
        Amount,
        InvoiceAmount
      }
    ],
    Notes: notes
  };

  const redeemResult = await redeemGiftCard({
    authToken,
    transactionId: redeemTransactionId,
    dateAtClient,
    payload
  });

  // Clean response
  const cleanResponse = {
    TransactionAmount: redeemResult?.Cards?.[0]?.TransactionAmount || Amount,
    Cards: [
      {
        Balance: redeemResult?.Cards?.[0]?.Balance
      }
    ]
  };

  return new AppSuccess(res, cleanResponse);
});

// Generates a random 15-character string for idempotency
function generateIdempotencyKey() {
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 15; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// Generates a 9-digit numeric transaction ID
function generateStringTransactionId() {
  const characters = "0123456789";
  let result = "";
  const charactersLength = characters.length;
  for (let i = 0; i < 9; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}



export const rewardifyAuthController = catchAsync(async (req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const {
    email,
    amount,
    memo,
    expiresAt, // optional
    sendEmail,
    emailNote
  } = req.body;
  console.log(req.body, "req.body");

  const rewardifyToken = req.headers.authorization?.replace("Bearer ", "");
  const customerId = req.params.customerId;

  console.log(rewardifyToken, customerId,  "rewardifyToken, customerId");

  // Validation
  if (
    !rewardifyToken || !customerId || !email || !amount ||
    !memo || sendEmail === undefined || !emailNote
  ) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "headers/body/params",
          message:
            "Authorization header, customerId (in URL), and email, amount, memo, sendEmail, and emailNote (in body) are required",
        },
      ],
    });
  }

  try {
    const result = await creditRewardifyStoreCredit({
      rewardifyToken,
      customerId,
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    });

    console.log(result, "result");
    return new AppSuccess(res, result);
  } catch (error) {
    console.error("Rewardify credit error:", error);
    const errRes = error.response?.data || error;
    throw new AppError("Failed to credit Rewardify store credit", error.response?.status || 500, {
      errors: [
        {
          field: "rewardify",
          message: errRes.message || JSON.stringify(errRes),
        },
      ],
    });
  }
});
