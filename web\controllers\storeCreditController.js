import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { authorizeQwikCilver, getGiftCardBalance, redeemGiftCard, rewardifyAuth, creditRewardifyStoreCredit, checkRewardifyCustomer } from "../services/storeCreditService.js";
import {
  validateCardData,
  createApiError,
  generateInvoiceData,
  generateStringTransactionId,
  createRedeemPayload,
  createRedeemNotes,
  extractAuthToken
} from "../utils/storeCreditHelpers.js";

export const qwikcilverAuth = catchAsync(async (_req, res) => {
  const transactionId = generateStringTransactionId();

  const result = await authorizeQwikCilver(transactionId);
  return new AppSuccess(res, {
    authToken: result.AuthToken,
    transactionId: transactionId
  });
});

export const getGiftCardBalanceController = catchAsync(async (req, res) => {
  // Validate only card data from frontend
  const { CardNumber: cardNumber, CardPIN: cardPIN } = validateCardData(req.body);

  // Generate transaction ID and get auth token from backend
  const transactionId = generateStringTransactionId();

  // First authenticate to get the auth token
  const authResult = await authorizeQwikCilver(transactionId);
  const authToken = authResult.AuthToken;

  try {
    const result = await getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN });
    const card = result.Cards[0];

    return new AppSuccess(res, {
      balance: card.Balance,
      cardStatus: card.CardStatus,
    });
  } catch (error) {
    throw createApiError("Failed to fetch gift card balance", error, "qwikcilver");
  }
});

export const redeemGiftCardController = catchAsync(async (req, res) => {
  // Validate only card data from frontend
  const { CardNumber, CardPin, Amount, InvoiceAmount } = validateCardData(req.body, ['CardNumber', 'CardPin', 'Amount', 'InvoiceAmount']);

  // Generate transaction IDs and get auth token from backend
  const balanceCheckTransactionId = generateStringTransactionId();
  const redeemTransactionId = generateStringTransactionId();
  const dateAtClient = new Date().toISOString();

  // First authenticate to get the auth token
  const authResult = await authorizeQwikCilver(balanceCheckTransactionId);
  const authToken = authResult.AuthToken;

  const { invoiceNumber, idempotencyKey } = generateInvoiceData();
  const notes = createRedeemNotes({ amount: Amount, invoiceAmount: InvoiceAmount, invoiceNumber });

  const balanceResult = await getGiftCardBalance({
    authToken,
    transactionId: balanceCheckTransactionId,
    cardNumber: CardNumber,
    cardPIN: CardPin,
  });

  if (!balanceResult.Cards || balanceResult.Cards.length === 0) {
    throw new AppError("Card not found or invalid", 404);
  }
  const payload = createRedeemPayload({
    invoiceNumber,
    idempotencyKey,
    cardData: { CardNumber, CardPin, Amount, InvoiceAmount },
    notes
  });

  const redeemResult = await redeemGiftCard({
    authToken,
    transactionId: redeemTransactionId,
    dateAtClient,
    payload
  });
  return new AppSuccess(res, {
    TransactionAmount: redeemResult?.Cards?.[0]?.TransactionAmount || Amount,
    Cards: [{ Balance: redeemResult?.Cards?.[0]?.Balance }]
  });
});





export const rewardifyAuthController = catchAsync(async (_req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const rewardifyToken = extractAuthToken(req.headers);
  const { customerId } = req.params;
  const { email, amount, memo, expiresAt, sendEmail, emailNote } = req.body;
  if (!customerId || !email || !amount || !memo || sendEmail === undefined || !emailNote) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "params/body",
          message: "customerId (in URL), and email, amount, memo, sendEmail, emailNote (in body) are required",
        },
      ],
    });
  }

  try {
    // Check if customer exists in Rewardify
    try {
      await checkRewardifyCustomer(rewardifyToken, customerId);
    } catch (customerError) {
      if (customerError.response?.status === 404) {
        throw new AppError("Customer not found in Rewardify", 404, {
          errors: [
            {
              field: "customerId",
              message: `Customer ID ${customerId} does not exist in Rewardify system`,
            },
          ],
        });
      }
    }
    const result = await creditRewardifyStoreCredit({
      rewardifyToken,
      customerId,
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    });

    return new AppSuccess(res, result);
  } catch (error) {
    if (error instanceof AppError) throw error;
    throw createApiError("Failed to credit Rewardify store credit", error, "rewardify");
  }
});
