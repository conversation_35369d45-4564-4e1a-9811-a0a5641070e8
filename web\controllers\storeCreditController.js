import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { authorizeQwikCilver, getGiftCardBalance, redeemGiftCard, rewardifyAuth, creditRewardifyStoreCredit } from "../services/storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";

export const qwikcilverAuth = catchAsync(async (req, res) => {
  // console.log("qwikcilverAuth called");
  // const storeData = await getATFromSQL();
  // console.log("storeDta", storeData);
  // const shop = process.env.SHOP;
  // console.log("shop", shop);
  // const AT = storeData.find((x) => x.shop === shop)?.accessToken;
  // if (!AT) {
  //   console.log("No access token found for shop", shop);
  //   throw new AppError("Authentication failed", 401, {
  //     errors: [
  //       {
  //         field: "auth",
  //         message: "Access token not found for the specified shop.",
  //       },
  //     ],
  //   });
  // }
  const { transactionId } = req.body;
  console.log("Transactio Id", transactionId);
  if (!transactionId) {
    console.log("No transactionId provided in body");
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "body",
          message: "transactionId is required in request body",
        },
      ],
    });
  }
  const result = await authorizeQwikCilver(transactionId);
  console.log("QwikCilver Auth result", result);
  return new AppSuccess(res, { authToken: result.AuthToken });
});

export const getGiftCardBalanceController = catchAsync(async (req, res) => {
  const { authorization, transactionid, dateatclient } = req.headers;
  const { Cards } = req.body;

  const authToken = authorization?.replace("Bearer ", "");
  const transactionId = transactionid;
  const dateAtClient = dateatclient;

  if (!authToken || !transactionId || !dateAtClient || !Cards || !Cards[0]?.CardNumber || !Cards[0]?.CardPIN) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "headers/body",
          message:
            "Authorization, TransactionId, DateAtClient (headers), and Cards[0] with CardNumber & CardPIN (body) are required",
        },
      ],
    });
  }

  const { CardNumber: cardNumber, CardPIN: cardPIN } = Cards[0];

  try {
    const result = await getGiftCardBalance({
      authToken,
      transactionId,
      cardNumber,
      cardPIN,
    });
    const card = result.Cards[0];

    return new AppSuccess(res, {
      balance: card.Balance,
      cardStatus: card.CardStatus,
    });
  } catch (error) {
    const errRes = error.response?.data || error;
    throw new AppError("Failed to fetch gift card balance", error.response?.status || 500, {
      errors: [
        {
          field: "qwikcilver",
          message: errRes.message || JSON.stringify(errRes),
        },
      ],
    });
  }
});


export const redeemGiftCardController = catchAsync(async (req, res) => {
  const { authorization, transactionid, dateatclient } = req.headers;
  const { Cards } = req.body;
  console.log("redeemGiftCardController called");

  const authToken = authorization?.replace("Bearer ", "");
  const transactionId = transactionid;
  const dateAtClient = dateatclient;
  console.log("redeemGiftCardController called 2");
  // Validate headers
  if (!authToken || !transactionId || !dateAtClient) {
    throw new AppError("Missing headers", 400, {
      errors: [
        { field: "headers", message: "Authorization, TransactionId, and DateAtClient are required" }
      ]
    });
  }

  // Validate Cards array
  if (!Cards || !Cards[0]?.CardNumber || !Cards[0]?.CardPin || !Cards[0]?.Amount || !Cards[0]?.InvoiceAmount) {
    throw new AppError("Missing card data", 400, {
      errors: [
        {
          field: "body.Cards",
          message: "Cards[0] with CardNumber, CardPin, Amount, and InvoiceAmount is required"
        }
      ]
    });
  }
  
    console.log("redeemGiftCardController called 3");
  // Extract card values
  const { CardNumber, CardPin, Amount, InvoiceAmount } = Cards[0];

  // Generate dynamic InvoiceNumber and IdempotencyKey
  const invoiceNumber = `INV-${Date.now()}`;
  const idempotencyKey = `${invoiceNumber}-${generateIdempotencyKey()}`;

  // Generate Notes string
  const notes = `{VldType~GCRDM|AMT~${Amount}|BillAmount~${InvoiceAmount}|InvoiceNumber~${invoiceNumber}}`;
    console.log("redeemGiftCardController called 4");

  // Validate card before redeem
  const balanceResult = await getGiftCardBalance({
    authToken,
    transactionId,
    dateAtClient,
    cardNumber: CardNumber,
    cardPIN: CardPin,
  });
  console.log("redeemGiftCardController called 5");
  if (!balanceResult.Cards || balanceResult.Cards.length === 0) {
    throw new AppError("Card not found or invalid", 404);
  }

  // Build redeem payload
  const payload = {
    TransactionTypeId: 302,
    InputType: 1,
    InvoiceNumber: invoiceNumber,
    IdempotencyKey: idempotencyKey,
    Cards: [
      {
        CardNumber,
        CardPin,
        Amount,
        InvoiceAmount
      }
    ],
    Notes: notes
  };
  console.log("redeemGiftCardController called 6" , payload);

  // Redeem card
  const redeemResult = await redeemGiftCard({
    authToken,
    transactionId,
    dateAtClient,
    payload
  });

  // Filter response
  const card = redeemResult?.Cards?.[0];
  console.log("redeemGiftCardController called 7" , card);

  return new AppSuccess(res, {
    balance: card?.Balance,
    cardStatus: card?.CardStatus,
    message: redeemResult?.ResponseMessage,
    invoiceNumber,
    idempotencyKey
  });
});

// Generates a random 15-character string for idempotency
function generateIdempotencyKey() {
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 15; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}



export const rewardifyAuthController = catchAsync(async (req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const { rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote } = req.body;
  if (!rewardifyToken || !customerId || !email || !amount || !memo || sendEmail === undefined || !emailNote) {
    throw new AppError("Invalid request", 400, {
      errors: [
        { field: "body", message: "rewardifyToken, customerId, email, amount, memo, sendEmail, and emailNote are required in request body" },
      ],
    });
  }
  const result = await creditRewardifyStoreCredit({ rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote });
  return new AppSuccess(res, result);
});