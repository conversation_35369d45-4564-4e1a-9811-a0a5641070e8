import AppError from "../middlewares/AppError.js";
import AppSuccess from "../middlewares/appSuccess.js";
import { catchAsync } from "../utils/helpers.js";
import { authorizeQwikCilver, getGiftCardBalance, redeemGiftCard, rewardifyAuth, creditRewardifyStoreCredit } from "../services/storeCreditService.js";
import { getATFromSQL } from "../middlewares/helpers.js";

export const qwikcilverAuth = catchAsync(async (req, res) => {
  // console.log("qwikcilverAuth called");
  // const storeData = await getATFromSQL();
  // console.log("storeDta", storeData);
  // const shop = process.env.SHOP;
  // console.log("shop", shop);
  // const AT = storeData.find((x) => x.shop === shop)?.accessToken;
  // if (!AT) {
  //   console.log("No access token found for shop", shop);
  //   throw new AppError("Authentication failed", 401, {
  //     errors: [
  //       {
  //         field: "auth",
  //         message: "Access token not found for the specified shop.",
  //       },
  //     ],
  //   });
  // }
  const { transactionId } = req.body;
  console.log("Transactio Id", transactionId);
  if (!transactionId) {
    console.log("No transactionId provided in body");
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "body",
          message: "transactionId is required in request body",
        },
      ],
    });
  }
  const result = await authorizeQwikCilver(transactionId);
  console.log("QwikCilver Auth result", result);
  return new AppSuccess(res, { authToken: result.AuthToken });
});

export const getGiftCardBalanceController = catchAsync(async (req, res) => {
  const { authToken, transactionId, cardNumber, cardPIN } = req.body;

  if (!authToken || !transactionId || !cardNumber || !cardPIN) {
    throw new AppError("Invalid request", 400, {
      errors: [
        {
          field: "body",
          message: "authToken, transactionId, cardNumber, and cardPIN are required in request body",
        },
      ],
    });
  }

  try {
    const result = await getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN });
    return new AppSuccess(res, result);
  } catch (error) {
    const errRes = error.response?.data || error;
    throw new AppError("Failed to fetch gift card balance", error.response?.status || 500, {
      errors: [
        {
          field: "qwikcilver",
          message: errRes.message || JSON.stringify(errRes),
        },
      ],
    });
  }
});

export const redeemGiftCardController = catchAsync(async (req, res) => {
  const { authToken, transactionId, invoiceNumber, idempotencyKey, cardNumber, cardPin, amount, invoiceAmount } = req.body;
  if (!authToken || !transactionId || !invoiceNumber || !idempotencyKey || !cardNumber || !cardPin || !amount || !invoiceAmount) {
    throw new AppError("Invalid request", 400, {
      errors: [
        { field: "body", message: "authToken, transactionId, invoiceNumber, idempotencyKey, cardNumber, cardPin, amount, and invoiceAmount are required in request body" },
      ],
    });
  }
  // Notes format: {VldType~GCRDM|AMT~XXX|BillAmount~XXX|InvoiceNumber~XXX}
  const notes = `{VldType~GCRDM|AMT~${amount}|BillAmount~${invoiceAmount}|InvoiceNumber~${invoiceNumber}}`;
  const result = await getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN: cardPin });
  if (!result.Cards || result.Cards.length === 0) {
    throw new AppError("Card not found or invalid", 404);
  }
  // Call redeemGiftCard service
  const redeemResult = await redeemGiftCard({
    authToken,
    transactionId,
    invoiceNumber,
    idempotencyKey,
    cardNumber,
    cardPin,
    amount,
    invoiceAmount,
    notes
  });
  return new AppSuccess(res, redeemResult);
});

export const rewardifyAuthController = catchAsync(async (req, res) => {
  const result = await rewardifyAuth();
  return new AppSuccess(res, result);
});

export const creditRewardifyStoreCreditController = catchAsync(async (req, res) => {
  const { rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote } = req.body;
  if (!rewardifyToken || !customerId || !email || !amount || !memo || sendEmail === undefined || !emailNote) {
    throw new AppError("Invalid request", 400, {
      errors: [
        { field: "body", message: "rewardifyToken, customerId, email, amount, memo, sendEmail, and emailNote are required in request body" },
      ],
    });
  }
  const result = await creditRewardifyStoreCredit({ rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote });
  return new AppSuccess(res, result);
});