export interface QwikCilverAuthRequest {
  TransactionId: string;
  UserName: string;
  Password: string;
  ForwardingEntityId: string;
  ForwardingEntityPassword: string;
  TerminalId: string;
}

export interface QwikCilverAuthResponse {
  AuthToken: string;
  TransactionStatus: boolean;
  ResponseMessage: string;
}

export interface QwikCilverConfig {
  apiUrl: string;
  username: string;
  password: string;
  forwardingEntityId: string;
  forwardingEntityPassword: string;
  terminalId: string;
} 