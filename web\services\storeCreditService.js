import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const QWIKCILVER_AUTH_URL = 'https://ajf2m0r1na8eau2bn0brcou5h2i0-custuatdev.qwikcilver.com/QwikCilver/XnP/api/v3/authorize';
const QWIKCILVER_BALANCE_URL = 'https://ajf2m0r1na8eau2bn0brcou5h2i0-custuatdev.qwikcilver.com/QwikCilver/XnP/api/v3/gc/transactions';
const REWARDIFY_AUTH_URL = 'https://api.rewardify.ca/oauth/v2/token';

export async function authorizeQwikCilver(transactionId) {
  const payload = {
    TransactionId: transactionId,
    UserName: process.env.QWIKCILVER_USERNAME,
    Password: process.env.QWIKCILVER_PASSWORD,
    ForwardingEntityId: process.env.QWIKCILVER_ENTITY_ID,
    ForwardingEntityPassword: process.env.QWIKCILVER_ENTITY_PASSWORD,
    TerminalId: process.env.QWIKCILVER_TERMINAL_ID,
  };
  const headers = {
    'Content-Type': 'application/json',
    'charset': 'UTF-8',
    'DateAtClient': new Date().toISOString(),
  };

  try {
    const response = await axios.post(QWIKCILVER_AUTH_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN }) {
  const payload = {
    TransactionTypeId: "306",
    InputType: "1",
    Cards: [
      {
        CardNumber: cardNumber,
        CardPIN: cardPIN
      }
    ]
  };

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'DateAtClient': new Date().toISOString(),
    'TransactionId': transactionId,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(QWIKCILVER_BALANCE_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function redeemGiftCard({ authToken, transactionId, dateAtClient, payload }) {
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'DateAtClient': dateAtClient,
    'TransactionId': transactionId,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(QWIKCILVER_BALANCE_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}


export async function rewardifyAuth() {
  const payload = {
    grant_type: 'client_credentials',
    client_id: process.env.REWARDIFY_CLIENT_ID,
    client_secret: process.env.REWARDIFY_CLIENT_SECRET
  };
  const headers = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(REWARDIFY_AUTH_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function checkRewardifyCustomer(rewardifyToken, customerId) {
  const url = `https://api.rewardify.ca/customer/${customerId}`;
  const headers = {
    'accept': 'application/json',
    'authorization': `Bearer ${rewardifyToken}`,
  };

  try {
    const response = await axios.get(url, { headers });
    return response.data;
  } catch (error) {
    console.error("Customer check failed:", {
      status: error.response?.status,
      data: error.response?.data
    });
    throw error;
  }
}

export async function creditRewardifyStoreCredit({ rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote }) {
  const url = `https://api.rewardify.ca/customer/${customerId}/account/credit`;

  const payload = {
    email: email.trim(),
    amount: amount,
    memo: memo.trim(),
    sendEmail: Boolean(sendEmail),
    emailNote: emailNote.trim()
  };
  if (expiresAt && expiresAt !== '') {
    payload.expiresAt = expiresAt;
  }

  const headers = {
    'accept': 'application/json',
    'authorization': `Bearer ${rewardifyToken}`,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.put(url, payload, { headers });
    return response.data;
  } catch (error) {
    console.error("Rewardify API Error Details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: url,
      payload: payload
    });

    let errorMessage = "Unknown Rewardify API error";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.response?.data?.title) {
      errorMessage = error.response.data.title;
    } else if (error.response?.statusText) {
      errorMessage = `${error.response.status} ${error.response.statusText}`;
    }
    const enhancedError = new Error(`Rewardify API Error: ${errorMessage}`);
    enhancedError.response = error.response;
    enhancedError.status = error.response?.status;
    throw enhancedError;
  }
}
