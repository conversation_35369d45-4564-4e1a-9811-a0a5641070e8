import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const QWIKCILVER_AUTH_URL = 'https://ajf2m0r1na8eau2bn0brcou5h2i0-custuatdev.qwikcilver.com/QwikCilver/XnP/api/v3/authorize';
const QWIKCILVER_BALANCE_URL = 'https://ajf2m0r1na8eau2bn0brcou5h2i0-custuatdev.qwikcilver.com/QwikCilver/XnP/api/v3/gc/transactions';
const REWARDIFY_AUTH_URL = 'https://api.rewardify.ca/oauth/v2/token';

export async function authorizeQwikCilver(transactionId) {
  const payload = {
    TransactionId: transactionId,
    UserName: process.env.QWIKCILVER_USERNAME,
    Password: process.env.QWIKCILVER_PASSWORD,
    ForwardingEntityId: process.env.QWIKCILVER_ENTITY_ID,
    ForwardingEntityPassword: process.env.QWIKCILVER_ENTITY_PASSWORD,
    TerminalId: process.env.QWIKCILVER_TERMINAL_ID,
  };

  console.log(payload)

  const headers = {
    'Content-Type': 'application/json',
    'charset': 'UTF-8',
    'DateAtClient': new Date().toISOString(),
  };

  try {
    const response = await axios.post(QWIKCILVER_AUTH_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function getGiftCardBalance({ authToken, transactionId, cardNumber, cardPIN }) {
  const payload = {
    TransactionTypeId: "306",
    InputType: "1",
    Cards: [
      {
        CardNumber: cardNumber,
        CardPIN: cardPIN
      }
    ]
  };

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'DateAtClient': new Date().toISOString(),
    'TransactionId': transactionId,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(QWIKCILVER_BALANCE_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function redeemGiftCard({ authToken, transactionId, invoiceNumber, idempotencyKey, cardNumber, cardPin, amount, invoiceAmount, notes }) {
  const payload = {
    TransactionTypeId: 302,
    InputType: 1,
    InvoiceNumber: invoiceNumber,
    IdempotencyKey: idempotencyKey,
    Cards: [
      {
        CardNumber: cardNumber,
        CardPin: cardPin,
        Amount: amount,
        InvoiceAmount: invoiceAmount
      }
    ],
    Notes: notes
  };

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'DateAtClient': new Date().toISOString(),
    'TransactionId': transactionId,
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(QWIKCILVER_BALANCE_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function rewardifyAuth() {
  const payload = {
    grant_type: 'client_credentials',
    client_id: process.env.REWARDIFY_CLIENT_ID,
    client_secret: process.env.REWARDIFY_CLIENT_SECRET
  };

  const headers = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
  };

  try {
    const response = await axios.post(REWARDIFY_AUTH_URL, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

export async function creditRewardifyStoreCredit({ rewardifyToken, customerId, email, amount, memo, expiresAt, sendEmail, emailNote }) {
  const url = `https://api.rewardify.ca/customer/${customerId}/account/credit`;
  const payload = {
    email,
    amount,
    memo,
    ...(expiresAt ? { expiresAt } : {}),
    sendEmail,
    emailNote
  };
  const headers = {
    'accept': 'application/json',
    'authorization': `Bearer ${rewardifyToken}`,
    'Content-Type': 'application/json',
  };
  try {
    const response = await axios.put(url, payload, { headers });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}
