import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";
import shopify from "./shopify.js";
import productCreator from "./product-creator.js";
import PrivacyWebhookHandlers from "./privacy.js";
import globalErrorHandler from "./middlewares/globalErrorHandler.js";
import KidsRouter from "./routes/kidsRouter.js";
import NewsletterRouter from "./routes/newsletterRouter.js";
import StoreCreditRouter from "./routes/storeCreditRouter.js";
import indexRouter from "./routes/indexRouter.js"
import * as dotenv from "dotenv";
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swagger.js';
import { simpleSwaggerSpec } from './simple-swagger.js';


dotenv.config();

const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000",
  10
);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();

app.use(express.json());

app.use("/kids", KidsRouter)
app.use("/api/store-credit", StoreCreditRouter)

app.use("/api/newsletter", (req, res, next) => {
  console.log(`Newsletter route hit: ${req.method} ${req.path}`);
  next();
}, NewsletterRouter);

// Set up Shopify authentication and webhook handling

app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

app.get("/api/healthcheck", async (req, res) => {
  console.log(
    "================================================================"
  );
  console.log("health check triggered");
  console.log(
    "================================================================"
  );
  res.status(200).send("HealthCheck - OK with EB!!!");
});

// Test route for Swagger
app.get("/api-docs-test", (req, res) => {
  res.json({
    message: "Swagger test route working",
    swaggerSpec: !!swaggerSpec,
    timestamp: new Date().toISOString()
  });
});


app.use("/index", indexRouter)

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js


// app.use("/api/*", shopify.validateAuthenticatedSession());

app.get("/api/products/count", async (_req, res) => {
  const client = new shopify.api.clients.Graphql({
    session: res.locals.shopify.session,
  });

  const countData = await client.request(`
    query shopifyProductCount {
      productsCount {
        count
      }
    }
  `);

  res.status(200).send({ count: countData.data.productsCount.count });
});

app.post("/api/products", async (_req, res) => {
  let status = 200;
  let error = null;

  try {
    await productCreator(res.locals.shopify.session);
  } catch (e) {
    console.log(`Failed to process products/create: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error });
});

// Swagger API Documentation
console.log('Setting up Swagger UI at /api-docs');
console.log('Swagger spec loaded:', !!swaggerSpec);

try {
  // Try with the generated spec first, fallback to simple spec
  const specToUse = swaggerSpec && Object.keys(swaggerSpec).length > 0 ? swaggerSpec : simpleSwaggerSpec;
  console.log('Using swagger spec:', specToUse ? 'loaded' : 'fallback');

  app.use('/api-docs', swaggerUi.serve);
  app.get('/api-docs', swaggerUi.setup(specToUse, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: "MiniKlub Store Credit API"
  }));
  console.log('Swagger UI setup completed successfully');
} catch (error) {
  console.error('Error setting up Swagger UI:', error);
  // Fallback setup
  app.use('/api-docs', swaggerUi.serve);
  app.get('/api-docs', swaggerUi.setup(simpleSwaggerSpec));
}

app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(
      readFileSync(join(STATIC_PATH, "index.html"))
        .toString()
        .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
    );
});

app.use(globalErrorHandler);

app.listen(PORT);
