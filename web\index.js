import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import cors from "cors";
import serveStatic from "serve-static";
import shopify from "./shopify.js";
import productCreator from "./product-creator.js";
import PrivacyWebhookHandlers from "./privacy.js";
import globalErrorHandler from "./middlewares/globalErrorHandler.js";
import KidsRouter from "./routes/kidsRouter.js";
import NewsletterRouter from "./routes/newsletterRouter.js";
import StoreCreditRouter from "./routes/storeCreditRouter.js";
import indexRouter from "./routes/indexRouter.js"
import * as dotenv from "dotenv";
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swagger.js';
import { simpleSwaggerSpec } from './simple-swagger.js';
import consolidatedSwaggerSpec from './consolidated-swagger.js';


dotenv.config();

const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000",
  10
);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();

// Trust proxy for Cloudflare
app.set('trust proxy', true);

// Enable CORS for all routes
app.use(cors({
  origin: '*', // Allow all origins for development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'TransactionId', 'DateAtClient']
}));

app.use(express.json());

app.use("/kids", KidsRouter)
app.use("/api/store-credit", StoreCreditRouter)

app.use("/api/newsletter", (req, res, next) => {
  console.log(`Newsletter route hit: ${req.method} ${req.path}`);
  next();
}, NewsletterRouter);

// Set up Shopify authentication and webhook handling

app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

app.get("/api/healthcheck", async (req, res) => {
  console.log(
    "================================================================"
  );
  console.log("health check triggered");
  console.log(
    "================================================================"
  );
  res.status(200).send("HealthCheck - OK with EB!!!");
});

// Test route for Swagger
app.get("/api/docs/test", (req, res) => {
  const host = req.get('host');
  let currentUrl;

  // Force HTTPS for Cloudflare tunnels
  if (host && host.includes('trycloudflare.com')) {
    currentUrl = `https://${host}`;
  } else {
    currentUrl = `${req.protocol}://${host}`;
  }

  res.json({
    message: "Swagger test route working",
    swaggerSpec: !!swaggerSpec,
    pathsCount: swaggerSpec?.paths ? Object.keys(swaggerSpec.paths).length : 0,
    availablePaths: swaggerSpec?.paths ? Object.keys(swaggerSpec.paths) : [],
    detectedProtocol: req.protocol,
    detectedHost: host,
    currentServerUrl: currentUrl,
    swaggerUrl: `${currentUrl}/api/docs`,
    timestamp: new Date().toISOString()
  });
});


app.use("/index", indexRouter)

app.post('/webhook/refund', (req, res) => {
  const refundData = req.body;
  console.log("🧾 Refund Webhook Received:");
  console.dir(refundData, { depth: null });
  const gateway = refundData?.transactions?.[0]?.gateway?.toLowerCase() || '';
  const isCOD = gateway.includes('Cash on Delivery (COD)');
  const orderType = isCOD ? 'COD' : 'Prepaid';

  console.log(`💳 Order Type: ${orderType}`);

  res.status(200).send('OK');
});

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js


// app.use("/api/*", shopify.validateAuthenticatedSession());

app.get("/api/products/count", async (_req, res) => {
  const client = new shopify.api.clients.Graphql({
    session: res.locals.shopify.session,
  });

  const countData = await client.request(`
    query shopifyProductCount {
      productsCount {
        count
      }
    }
  `);

  res.status(200).send({ count: countData.data.productsCount.count });
});

app.post("/api/products", async (_req, res) => {
  let status = 200;
  let error = null;

  try {
    await productCreator(res.locals.shopify.session);
  } catch (e) {
    console.log(`Failed to process products/create: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error });
});

// Swagger API Documentation
console.log('Setting up Swagger UI at /api-docs');
console.log('Swagger spec loaded:', !!swaggerSpec);

try {
  // Try with the generated spec first, then consolidated, then simple fallback
  let specToUse = swaggerSpec;

  // Check if the main spec has paths
  if (!swaggerSpec?.paths || Object.keys(swaggerSpec.paths).length === 0) {
    console.log('Main swagger spec has no paths, trying consolidated spec');
    specToUse = consolidatedSwaggerSpec;
  }

  // Final fallback to simple spec
  if (!specToUse?.paths || Object.keys(specToUse.paths).length === 0) {
    console.log('Consolidated spec also has no paths, using simple fallback');
    specToUse = simpleSwaggerSpec;
  }

  console.log('Using swagger spec with', Object.keys(specToUse?.paths || {}).length, 'paths');

  app.use('/api/docs', swaggerUi.serve);
  app.get('/api/docs', (req, res, next) => {
    // Dynamically update server URL in the spec
    const host = req.get('host');
    let currentUrl;

    // Force HTTPS for Cloudflare tunnels
    if (host && host.includes('trycloudflare.com')) {
      currentUrl = `https://${host}`;
    } else {
      currentUrl = `${req.protocol}://${host}`;
    }

    console.log('Dynamic Swagger URL:', currentUrl);

    const dynamicSpec = {
      ...specToUse,
      servers: [
        {
          url: currentUrl,
          description: 'Current server'
        },
        {
          url: 'http://localhost:3000',
          description: 'Local development server'
        }
      ]
    };

    // Setup swagger UI with dynamic spec
    swaggerUi.setup(dynamicSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: "MiniKlub Store Credit API"
    })(req, res, next);
  });
  console.log('Swagger UI setup completed successfully');
} catch (error) {
  console.error('Error setting up Swagger UI:', error);
  // Final fallback setup
  app.use('/api/docs', swaggerUi.serve);
  app.get('/api/docs', swaggerUi.setup(consolidatedSwaggerSpec));
}

app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

// app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
//   return res
//     .status(200)
//     .set("Content-Type", "text/html")
//     .send(
//       readFileSync(join(STATIC_PATH, "index.html"))
//         .toString()
//         .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
//     );
// });

app.use(globalErrorHandler);

app.listen(PORT);
