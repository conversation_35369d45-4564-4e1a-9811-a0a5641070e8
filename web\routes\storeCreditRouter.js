import express from "express";
import { qwikcilver<PERSON><PERSON>, getGiftCardBalanceController, redeemGiftCardController, rewardifyAuthController, creditRewardifyStoreCreditController } from "../controllers/storeCreditController.js";
const router = express.Router();

// POST /api/store-credit/auth
router.post('/auth',
    qwikcilverAuth);

// POST /api/store-credit/giftcard-balance
router.post('/balance', getGiftCardBalanceController);

// POST /api/store-credit/redeem-giftcard
router.post('/redeem-giftcard', redeemGiftCardController);

// POST /api/store-credit/rewardify-auth
router.post('/rewardify-auth', rewardifyAuthController);

// PUT /api/store-credit/rewardify-credit
router.put('/rewardify-credit', creditRewardifyStoreCreditController);

export default router;