import express from "express";
import { qwikcilver<PERSON><PERSON>, getGiftCardBalanceController, redeemGiftCardController, rewardifyAuthController, creditRewardifyStoreCreditController } from "../controllers/storeCreditController.js";

/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management
 */

const router = express.Router();

// QwikCilver Authentication
router.post('/auth', qwikcilverAuth);

// Gift Card Balance Check
router.post('/balance', getGiftCardBalanceController);

// Gift Card Redemption
router.post('/redeem-giftcard', redeemGiftCardController);

// Rewardify Authentication
router.post('/rewardify-auth', rewardifyAuthController);

// Rewardify Store Credit
router.put('/rewardify-credit/:customerId', creditRewardifyStoreCreditController);

export default router;