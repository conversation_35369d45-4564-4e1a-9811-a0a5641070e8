import express from "express";
import { qwikcilver<PERSON>uth, getGiftCardBalanceController, redeemGiftCardController, rewardifyAuthController, creditRewardifyStoreCreditController } from "../controllers/storeCreditController.js";
const router = express.Router();

router.post('/auth',
    qwikcilverAuth);
router.post('/balance', getGiftCardBalanceController);
router.post('/redeem-giftcard', redeemGiftCardController);
router.post('/rewardify-auth', rewardifyAuthController);
router.put('/rewardify-credit/:customerId', creditRewardifyStoreCreditController);

export default router;