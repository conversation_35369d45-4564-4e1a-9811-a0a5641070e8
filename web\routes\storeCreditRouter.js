import express from "express";
import { qwikcilver<PERSON><PERSON>, getGiftCardBalanceController, redeemGiftCardController, rewardifyAuthController, creditRewardifyStoreCreditController } from "../controllers/storeCreditController.js";

/**
 * @swagger
 * tags:
 *   - name: Store Credit
 *     description: Store credit and gift card management
 */

const router = express.Router();
router.post('/auth', qwikcilverAuth);
router.post('/balance', getGiftCardBalanceController);
router.post('/redeem-giftcard', redeemGiftCardController);
router.post('/rewardify-auth', rewardifyAuthController);
router.put('/rewardify-credit/:customerId', creditRewardifyStoreCreditController);

export default router;