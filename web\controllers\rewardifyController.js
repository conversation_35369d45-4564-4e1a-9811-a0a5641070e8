import { creditCustomerInRewardify, getRewardifyOrders } from "../services/rewardifyService.js";
import { rewardifyClient } from "../utils/apiClient.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import shopify from "../shopify.js";

// GraphQL query to fetch Shopify order details
const GET_ORDER_QUERY = `
  query getOrder($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      createdAt
      cancelledAt
      cancelReason
      confirmed

      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }

      customer {
        id
        email
      }
      email

      lineItems(first: 50) {
        edges {
          node {
            title
            quantity
            variant {
              id
              title
              sku
              price
            }
            discountedUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }

      transactions {
        id
        kind
        status
        gateway
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        createdAt
      }

      discountApplications(first: 10) {
        edges {
          node {
           __typename
            ... on DiscountCodeApplication {
              code
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              title
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
          }
        }
      }
    }
  }
`;




export const fetchRewardifyTransactions = async (req, res) => {
  const { orderId } = req.params;
  const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

  if (!orderId) {
    return res.status(400).json({ message: "Missing orderId in URL params" });
  }

  if (!token) {
    return res.status(401).json({ message: "Missing Authorization token in headers" });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    console.log("Raw response from getRewardifyOrders:", response);
    console.log("Response type:", typeof response);
    console.log("Is Array:", Array.isArray(response));

    // The apiClient already returns response.data, so response IS the data
    const transactions = Array.isArray(response) ? response : [];

    console.log("Final transactions:", transactions);

    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    res.status(200).json({
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    console.error("Error in fetchRewardifyTransactions:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to fetch Rewardify transactions" });
  }
};

export const creditStoreCreditToCustomer = async (req, res) => {
  try {
    const shopifyCustomerId = req.params.shopifyCustomerId;
    const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

    
    if (!token) {
      return res.status(401).json({ message: "Missing Authorization token in headers" });
    }

    const {
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    } = req.body;

    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    res.status(200).json({ transaction });
  } catch (error) {
    console.error("Error in creditStoreCreditToCustomer:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to credit customer" });
  }
};

// Test Shopify authentication and scopes
export const testShopifyAuth = async (req, res) => {
  try {
    console.log("🔧 Testing Shopify authentication...");

    // Get shop and access token
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;

    console.log("🔧 Auth Debug:");
    console.log("- SHOP env var:", shop);
    console.log("- Store data:", storeData);

    if (!storeData || storeData.length === 0) {
      return res.status(401).json({
        message: "No Shopify sessions found in database",
        suggestion: "Please install/reinstall the Shopify app",
        debug: {
          shopFromEnv: shop,
          storeDataExists: !!storeData,
          storeDataLength: storeData?.length || 0
        }
      });
    }

    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        message: "Access token not found for shop",
        availableShops: storeData.map(x => x.shop),
        requestedShop: shop,
        suggestion: "Check SHOP environment variable or reinstall app"
      });
    }

    // Test a simple GraphQL query
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });

    const testQuery = `
      query {
        shop {
          id
          name
          email
          myshopifyDomain
        }
      }
    `;

    const { data } = await client.request(testQuery);

    res.status(200).json({
      message: "Shopify authentication successful",
      shop: data.shop,
      accessTokenLength: AT.length,
      hasReadOrdersScope: "Need to check manually - try fetching an order",
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Shopify auth test failed:", error);

    res.status(500).json({
      message: "Shopify authentication test failed",
      error: error.message,
      suggestions: [
        "Check if Shopify app is installed",
        "Verify SHOP environment variable",
        "Check if access token is valid",
        "Ensure required scopes are granted"
      ]
    });
  }
};

// Simple Shopify order fetch function for testing
export const fetchRewardifyOrder = async (req, res) => {
  const { orderId } = req.params;

  if (!orderId) {
    return res.status(400).json({
      message: "Missing orderId in URL params",
      example: "GET /api/rewardify/order/12345"
    });
  }

  try {
    console.log(`🔍 Fetching Shopify order: ${orderId}`);

    // Get shop and access token
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;

    console.log("🔧 Debug info:");
    console.log("- Shop from env:", shop);
    console.log("- Store data count:", storeData?.length || 0);
    console.log("- Available shops:", storeData?.map(x => x.shop) || []);

    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        message: "Authentication failed",
        error: "Access token not found for the specified shop",
        debug: {
          shopFromEnv: shop,
          availableShops: storeData?.map(x => x.shop) || [],
          storeDataCount: storeData?.length || 0
        }
      });
    }

    console.log("✅ Access token found, length:", AT?.length || 0);

    // Create Shopify GraphQL client
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });

    // Convert orderId to Shopify GID format if it's just a number
    const shopifyOrderId = orderId.startsWith('gid://')
      ? orderId
      : `gid://shopify/Order/${orderId}`;

    console.log(`� Querying Shopify order with ID: ${shopifyOrderId}`);

    // Fetch order from Shopify
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });

    console.log("📊 GraphQL response received");

    if (!data.order) {
      return res.status(404).json({
        message: "Order not found",
        orderId,
        shopifyOrderId,
        note: "Order may not exist or you may not have permission to access it"
      });
    }

    console.log("✅ Shopify order fetch successful");

    res.status(200).json({
      message: "Shopify order fetched successfully",
      orderId,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("❌ Error fetching Shopify order:", error);
    console.error("❌ Error details:", {
      message: error.message,
      response: error?.response?.data,
      body: error?.body,
      graphQLErrors: error?.body?.errors?.graphQLErrors
    });

    let statusCode = 500;
    let errorMessage = "Failed to fetch order";
    let suggestions = [];

    // Handle different types of errors
    if (error.message?.includes("401 Unauthorized")) {
      statusCode = 401;
      errorMessage = "Unauthorized access to Shopify API";
      suggestions = [
        "Check if the Shopify app is properly installed",
        "Verify that the access token is valid",
        "Ensure the app has 'read_orders' scope",
        "Try reinstalling the Shopify app"
      ];
    } else if (error.message?.includes("404")) {
      statusCode = 404;
      errorMessage = "Order not found";
      suggestions = [
        "Verify the order ID is correct",
        "Check if the order exists in your Shopify store",
        "Ensure you have permission to access this order"
      ];
    } else if (error?.body?.errors?.graphQLErrors) {
      errorMessage = error.body.errors.graphQLErrors[0]?.message || "GraphQL error";
      suggestions = ["Check the GraphQL query syntax and permissions"];
    }

    res.status(statusCode).json({
      message: "Failed to fetch Shopify order",
      error: errorMessage,
      orderId,
      statusCode,
      suggestions,
      debug: {
        shopifyOrderId: orderId.startsWith('gid://') ? orderId : `gid://shopify/Order/${orderId}`,
        errorType: error.constructor.name,
        hasAccessToken: !!process.env.SHOP
      }
    });
  }
};

// Process proportional store credit refund
export const processProportionalStoreCredit = async (orderId, customerId, refundedItems) => {
  try {
    console.log(`🔄 Processing proportional store credit refund for order: ${orderId}`);
    console.log(`👤 Customer ID: ${customerId}`);
    console.log(`📦 Refunded items:`, refundedItems);

    // Step 1: Check if order used store credit
    console.log("🔍 Step 1: Checking if order used store credit...");

    const transactionsResponse = await fetch(
      `${process.env.BASE_URL || 'https://modular-maria-once-problem.trycloudflare.com'}/api/rewardify/order/${orderId}/transactions`
    );

    if (!transactionsResponse.ok) {
      throw new Error(`Failed to fetch transactions: ${transactionsResponse.status}`);
    }

    const transactionsData = await transactionsResponse.json();

    if (!transactionsData.foundDiscountRedemption) {
      console.log("ℹ️ No store credit was used in this order, skipping refund");
      return {
        success: true,
        message: "No store credit refund needed - order didn't use store credit",
        refundAmount: 0
      };
    }

    console.log("✅ Store credit was used in this order");
    console.log("💰 Store credit transactions:", transactionsData.transactions);

    // Step 2: Get order details to calculate proportions
    console.log("🔍 Step 2: Getting order details...");

    const orderResponse = await fetch(
      `${process.env.BASE_URL || 'https://modular-maria-once-problem.trycloudflare.com'}/api/rewardify/order/${orderId}`
    );

    if (!orderResponse.ok) {
      throw new Error(`Failed to fetch order details: ${orderResponse.status}`);
    }

    const orderData = await orderResponse.json();
    const order = orderData.orderData;

    // Step 3: Calculate total discount amount used
    let totalDiscountAmount = 0;
    if (order.discountApplications?.edges?.length > 0) {
      for (const discountEdge of order.discountApplications.edges) {
        const discount = discountEdge.node;
        if (discount.value?.amount) {
          totalDiscountAmount += parseFloat(discount.value.amount);
        }
      }
    }

    console.log(`💸 Total discount amount used: ₹${totalDiscountAmount}`);

    if (totalDiscountAmount === 0) {
      console.log("ℹ️ No discount amount found, skipping refund");
      return {
        success: true,
        message: "No store credit refund needed - no discount amount found",
        refundAmount: 0
      };
    }

    // Step 4: Calculate total order subtotal (before discount)
    let totalOrderSubtotal = 0;
    const productPrices = {};

    for (const lineItemEdge of order.lineItems.edges) {
      const lineItem = lineItemEdge.node;
      const productPrice = parseFloat(lineItem.variant.price) * lineItem.quantity;
      totalOrderSubtotal += productPrice;

      // Store product prices for proportion calculation
      productPrices[lineItem.variant.id] = {
        unitPrice: parseFloat(lineItem.variant.price),
        quantity: lineItem.quantity,
        totalPrice: productPrice,
        title: lineItem.title
      };
    }

    console.log(`🛒 Total order subtotal: ₹${totalOrderSubtotal}`);
    console.log(`📊 Product prices:`, productPrices);

    // Step 5: Calculate proportional refund for each refunded item
    let totalRefundAmount = 0;
    const refundDetails = [];

    for (const refundedItem of refundedItems) {
      const { line_item_id, quantity, subtotal } = refundedItem;

      // Find matching product in order by comparing subtotal amounts
      // This is an approximation since we don't have direct line_item_id mapping
      let matchingProduct = null;
      let bestMatch = null;
      let smallestDifference = Infinity;

      for (const [variantId, product] of Object.entries(productPrices)) {
        const difference = Math.abs(product.totalPrice - subtotal);
        if (difference < smallestDifference) {
          smallestDifference = difference;
          bestMatch = [variantId, product];
        }
      }

      if (bestMatch && smallestDifference < 1) { // Allow small rounding differences
        matchingProduct = bestMatch;
      }

      if (!matchingProduct) {
        console.log(`⚠️ Could not find matching product for refunded item with subtotal: ₹${subtotal}`);
        continue;
      }

      const [variantId, productInfo] = matchingProduct;

      // Calculate proportion of discount for this product
      const productProportion = productInfo.totalPrice / totalOrderSubtotal;
      const productDiscountAmount = totalDiscountAmount * productProportion;

      // Calculate refund proportion based on quantity refunded
      const refundProportion = quantity / productInfo.quantity;
      const refundDiscountAmount = productDiscountAmount * refundProportion;

      totalRefundAmount += refundDiscountAmount;

      refundDetails.push({
        productTitle: productInfo.title,
        variantId,
        originalPrice: productInfo.totalPrice,
        refundedQuantity: quantity,
        totalQuantity: productInfo.quantity,
        productProportion: productProportion.toFixed(4),
        productDiscountAmount: productDiscountAmount.toFixed(2),
        refundProportion: refundProportion.toFixed(4),
        refundDiscountAmount: refundDiscountAmount.toFixed(2)
      });
    }

    console.log(`💰 Total store credit refund amount: ₹${totalRefundAmount.toFixed(2)}`);
    console.log(`📋 Refund details:`, refundDetails);

    // Step 6: Credit store credit back to customer account
    if (totalRefundAmount > 0) {
      console.log("💳 Step 6: Crediting store credit back to customer...");

      // We need to get customer email from order
      const customerEmail = order.customer?.email || order.email;

      if (!customerEmail) {
        throw new Error("Customer email not found in order");
      }

      // Use existing credit function
      const creditResult = await creditCustomerInRewardify({
        token: process.env.REWARDIFY_TOKEN, // You'll need to set this
        shopifyCustomerId: customerId,
        customerEmail: customerEmail,
        amount: totalRefundAmount.toFixed(2),
        itemTitle: `Store Credit Refund - Order #${order.name}`,
        orderId: orderId
      });

      console.log("✅ Store credit refunded successfully:", creditResult);

      return {
        success: true,
        message: "Store credit refunded successfully",
        refundAmount: totalRefundAmount.toFixed(2),
        refundDetails,
        creditResult
      };
    } else {
      return {
        success: true,
        message: "No store credit refund needed - calculated amount is 0",
        refundAmount: 0,
        refundDetails
      };
    }

  } catch (error) {
    console.error("❌ Error processing proportional store credit refund:", error);
    throw error;
  }
};

// Test endpoint for proportional store credit refund
export const testProportionalRefund = async (req, res) => {
  try {
    const { orderId, customerId, refundedItems } = req.body;

    console.log("🧪 Testing proportional store credit refund...");
    console.log("📋 Test Parameters:", { orderId, customerId, refundedItems });

    if (!orderId || !customerId || !refundedItems) {
      return res.status(400).json({
        message: "Missing required parameters",
        required: {
          orderId: "string - Shopify order ID",
          customerId: "string - Shopify customer ID (numeric)",
          refundedItems: "array - [{line_item_id, quantity, subtotal}]"
        },
        example: {
          orderId: "11738059800945",
          customerId: "7408855441649",
          refundedItems: [
            {
              line_item_id: 34884188537201,
              quantity: 1,
              subtotal: 400.00
            }
          ]
        }
      });
    }

    const result = await processProportionalStoreCredit(orderId, customerId, refundedItems);

    res.status(200).json({
      message: "Test completed successfully",
      testParameters: { orderId, customerId, refundedItems },
      result
    });

  } catch (error) {
    console.error("❌ Test failed:", error);
    res.status(500).json({
      message: "Test failed",
      error: error.message,
      stack: error.stack
    });
  }
};

// Test webhook simulation
export const testWebhookSimulation = async (req, res) => {
  try {
    console.log("🔔 Simulating webhook refund...");

    // Sample webhook data structure
    const sampleWebhookData = {
      id: 1100996378993,
      order_id: 11738059800945,
      customer: {
        id: "gid://shopify/Customer/7408855441649"
      },
      email: "<EMAIL>",
      refund_line_items: [
        {
          id: 1006936031601,
          line_item_id: 34884188537201,
          quantity: 1,
          subtotal: 400.00
        }
      ],
      gateway: "manual",
      total_price: "400.00"
    };

    // Use provided data or sample data
    const webhookData = req.body.webhookData || sampleWebhookData;

    console.log("📋 Webhook data:", webhookData);

    const orderId = webhookData.order_id;
    const refundedLineItems = webhookData.refund_line_items || [];
    const shopifyCustomerId = webhookData.customer?.id;

    if (!orderId || !refundedLineItems.length || !shopifyCustomerId) {
      return res.status(400).json({
        message: "Invalid webhook data",
        received: { orderId, refundedLineItems: refundedLineItems.length, shopifyCustomerId }
      });
    }

    // Convert Shopify customer ID to numeric format
    const numericCustomerId = shopifyCustomerId?.toString().replace('gid://shopify/Customer/', '');

    console.log("💳 Processing proportional store credit refund...");

    const refundResult = await processProportionalStoreCredit(
      orderId,
      numericCustomerId,
      refundedLineItems
    );

    console.log("✅ Webhook simulation completed:", refundResult);

    res.status(200).json({
      message: "Webhook simulation completed successfully",
      webhookData,
      refundResult
    });

  } catch (error) {
    console.error("❌ Webhook simulation failed:", error);
    res.status(500).json({
      message: "Webhook simulation failed",
      error: error.message
    });
  }
};


