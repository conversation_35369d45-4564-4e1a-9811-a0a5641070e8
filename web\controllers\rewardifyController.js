import { creditCustomerInRewardify, getRewardifyOrders } from "../services/rewardifyService.js";
import { rewardifyClient } from "../utils/apiClient.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import shopify from "../shopify.js";

// GraphQL query to fetch Shopify order details
const GET_ORDER_QUERY = `
  query getOrder($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      createdAt
      cancelledAt
      cancelReason
      confirmed

      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }

      customer {
        id
        email
      }
      email

      lineItems(first: 50) {
        edges {
          node {
            title
            quantity
            variant {
              id
              title
              sku
              price
            }
            discountedUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }

      transactions {
        id
        kind
        status
        gateway
        amountSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        createdAt
      }

      discountApplications(first: 10) {
        edges {
          node {
           __typename
            ... on DiscountCodeApplication {
              code
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              title
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
          }
        }
      }
    }
  }
`;




export const fetchRewardifyTransactions = async (req, res) => {
  const { orderId } = req.params;
  const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

  if (!orderId) {
    return res.status(400).json({ message: "Missing orderId in URL params" });
  }

  if (!token) {
    return res.status(401).json({ message: "Missing Authorization token in headers" });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    const transactions = Array.isArray(response) ? response : [];
    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );
    res.status(200).json({
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    console.error("Error in fetchRewardifyTransactions:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to fetch Rewardify transactions" });
  }
};

export const creditStoreCreditToCustomer = async (req, res) => {
  try {
    console.log("Credit customer data")
    const shopifyCustomerId = req.params.shopifyCustomerId;
    const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

    if (!token) {
      return res.status(401).json({ message: "Missing Authorization token in headers" });
    }

    // Check if this is a webhook refund request (contains webhookData)
    console.log("🔍 Checking if this is a webhook refund request..." , req.body);
    if (req.body.webhookData) {
      console.log("💳 Processing refund webhook for proportional store credit...");

      try {
        // Call proportionate amount calculation function
        const calculationResult = await processProportionalStoreCredit(req.body.webhookData);

        if (!calculationResult.shouldCredit) {
          console.log("ℹ️ No store credit refund needed");
          return res.status(200).json({
            message: calculationResult.message,
            refundAmount: calculationResult.refundAmount,
            processed: false
          });
        }

        // Credit the proportional amount to customer account
        console.log("💰 Crediting proportional store credit to customer...");

        const { customerData, refundAmount } = calculationResult;

        const response = await creditCustomerInRewardify({
          token,
          shopifyCustomerId: customerData.customerId,
          customerEmail: customerData.customerEmail,
          amount: refundAmount,
          itemTitle: `${calculationResult.isCOD ? 'COD' : 'Store Credit'} Refund - Order #${customerData.orderName}`,
          orderId: customerData.orderId,
        });

        const transaction = response.data?.transaction || {
          transactionType: "API Manual",
          amount: parseFloat(refundAmount).toFixed(4),
          amountCurrency: "INR"
        };

        console.log("✅ Proportional store credit refund completed successfully");

        return res.status(200).json({
          message: "Proportional store credit refund processed successfully",
          refundAmount,
          refundDetails: calculationResult.refundDetails,
          transaction,
          processed: true
        });

      } catch (error) {
        console.error("❌ Error processing proportional refund:", error);
        return res.status(500).json({
          message: "Failed to process proportional refund",
          error: error.message
        });
      }
    }

    // Regular manual credit request (existing functionality)
    const {
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    } = req.body;

    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    res.status(200).json({ transaction });
  } catch (error) {
    console.error("Error in creditStoreCreditToCustomer:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to credit customer" });
  }
};

// Test Shopify authentication and scopes
export const testShopifyAuth = async (req, res) => {
  try {
    console.log("🔧 Testing Shopify authentication...");

    // Get shop and access token
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;

    console.log("🔧 Auth Debug:");
    console.log("- SHOP env var:", shop);
    console.log("- Store data:", storeData);

    if (!storeData || storeData.length === 0) {
      return res.status(401).json({
        message: "No Shopify sessions found in database",
        suggestion: "Please install/reinstall the Shopify app",
        debug: {
          shopFromEnv: shop,
          storeDataExists: !!storeData,
          storeDataLength: storeData?.length || 0
        }
      });
    }

    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        message: "Access token not found for shop",
        availableShops: storeData.map(x => x.shop),
        requestedShop: shop,
        suggestion: "Check SHOP environment variable or reinstall app"
      });
    }

    // Test a simple GraphQL query
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });

    const testQuery = `
      query {
        shop {
          id
          name
          email
          myshopifyDomain
        }
      }
    `;

    const { data } = await client.request(testQuery);

    res.status(200).json({
      message: "Shopify authentication successful",
      shop: data.shop,
      accessTokenLength: AT.length,
      hasReadOrdersScope: "Need to check manually - try fetching an order",
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Shopify auth test failed:", error);

    res.status(500).json({
      message: "Shopify authentication test failed",
      error: error.message,
      suggestions: [
        "Check if Shopify app is installed",
        "Verify SHOP environment variable",
        "Check if access token is valid",
        "Ensure required scopes are granted"
      ]
    });
  }
};

// Simple Shopify order fetch function for testing
export const fetchRewardifyOrder = async (req, res) => {
  const { orderId } = req.params;

  if (!orderId) {
    return res.status(400).json({
      message: "Missing orderId in URL params",
      example: "GET /api/rewardify/order/12345"
    });
  }

  try {
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        message: "Authentication failed",
        error: "Access token not found for the specified shop",
        debug: {
          shopFromEnv: shop,
          availableShops: storeData?.map(x => x.shop) || [],
          storeDataCount: storeData?.length || 0
        }
      });
    }
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });
    const shopifyOrderId = orderId.startsWith('gid://')
      ? orderId
      : `gid://shopify/Order/${orderId}`;
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });
    if (!data.order) {
      return res.status(404).json({
        message: "Order not found",
        orderId,
        shopifyOrderId,
        note: "Order may not exist or you may not have permission to access it"
      });
    }
    res.status(200).json({
      message: "Shopify order fetched successfully",
      orderId,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("❌ Error fetching Shopify order:", error);
    console.error("❌ Error details:", {
      message: error.message,
      response: error?.response?.data,
      body: error?.body,
      graphQLErrors: error?.body?.errors?.graphQLErrors
    });

    let statusCode = 500;
    let errorMessage = "Failed to fetch order";
    let suggestions = [];

    // Handle different types of errors
    if (error.message?.includes("401 Unauthorized")) {
      statusCode = 401;
      errorMessage = "Unauthorized access to Shopify API";
      suggestions = [
        "Check if the Shopify app is properly installed",
        "Verify that the access token is valid",
        "Ensure the app has 'read_orders' scope",
        "Try reinstalling the Shopify app"
      ];
    } else if (error.message?.includes("404")) {
      statusCode = 404;
      errorMessage = "Order not found";
      suggestions = [
        "Verify the order ID is correct",
        "Check if the order exists in your Shopify store",
        "Ensure you have permission to access this order"
      ];
    } else if (error?.body?.errors?.graphQLErrors) {
      errorMessage = error.body.errors.graphQLErrors[0]?.message || "GraphQL error";
      suggestions = ["Check the GraphQL query syntax and permissions"];
    }

    res.status(statusCode).json({
      message: "Failed to fetch Shopify order",
      error: errorMessage,
      orderId,
      statusCode,
      suggestions,
      debug: {
        shopifyOrderId: orderId.startsWith('gid://') ? orderId : `gid://shopify/Order/${orderId}`,
        errorType: error.constructor.name,
        hasAccessToken: !!process.env.SHOP
      }
    });
  }
};

// Process proportional store credit refund calculation
export const processProportionalStoreCredit = async (webhookData) => {
  try {
    console.log(`🔄 Processing proportional store credit refund from webhook data`);
    const orderId = webhookData.order_id;
    console.log(`📦 Order ID: ${orderId}`);
    const refundedLineItems = webhookData.refund_line_items || [];
    const customerEmail = webhookData.email;
    const refundTransactions = webhookData.transactions || [];

    if (!orderId || !refundedLineItems.length) {
      throw new Error("Missing required webhook data: orderId or refundedLineItems");
    }

    const gateway = refundTransactions.length > 0 ? refundTransactions[0].gateway : null;
    const isCOD = gateway && gateway.toLowerCase().includes('cash on delivery');

    console.log(`📋 Webhook Data Extracted:`);
    console.log(`- Order ID: ${orderId}`);
    console.log(`- Customer Email: ${customerEmail}`);
    console.log(`- Gateway: ${gateway}`);
    console.log(`- Is COD: ${isCOD}`);
    console.log(`- Refunded Items:`, refundedLineItems);

    // Step 1: Get Rewardify access token
    console.log("🔍 Step 1: Getting Rewardify access token...");

    const authResponse = await fetch(
      `${process.env.BASE_URL || 'https://maryland-tongue-rope-iii.trycloudflare.com'}/api/store-credit/rewardify-auth`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!authResponse.ok) {
      throw new Error(`Failed to get auth token: ${authResponse.status}`);
    }

    const authData = await authResponse.json();
    const accessToken = authData.data?.access_token;

    if (!accessToken) {
      throw new Error("No access token received from auth API");
    }

    console.log("✅ Access token obtained successfully");

    // Step 2: Check if order used store credit via transactions API
    console.log("🔍 Step 2: Checking if order used store credit...");

    const transactionsResponse = await fetch(
      `${process.env.BASE_URL || 'https://maryland-tongue-rope-iii.trycloudflare.com'}/api/rewardify/order/${orderId}/transactions`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!transactionsResponse.ok) {
      throw new Error(`Failed to fetch transactions: ${transactionsResponse.status}`);
    }

    const transactionsData = await transactionsResponse.json();

    // Check if store credit was used and get the amount + customer ID
    let storeCreditUsedAmount = 0;
    let transactionCustomerId = null;

    if (transactionsData.foundDiscountRedemption) {
      console.log("✅ Store credit was used in this order");
      console.log("💰 Store credit transactions:", transactionsData.transactions);

      // Calculate total store credit used from transactions and get customer ID
      for (const transaction of transactionsData.transactions) {
        if (transaction.transactionType === "Discount Redemption" && transaction.amount) {
          // Amount is negative in the API response, so we make it positive
          storeCreditUsedAmount += Math.abs(parseFloat(transaction.amount));

          // Get shopifyCustomerId from transaction
          if (transaction.shopifyCustomerId && !transactionCustomerId) {
            transactionCustomerId = transaction.shopifyCustomerId;
          }
        }
      }

      console.log(`💳 Total store credit used: ₹${storeCreditUsedAmount}`);
      console.log(`👤 Shopify Customer ID from transactions: ${transactionCustomerId}`);
    } else {
      console.log("ℹ️ No store credit was used in this order, skipping refund");
      return {
        success: true,
        message: "No store credit refund needed - order didn't use store credit",
        refundAmount: 0,
        shouldCredit: false
      };
    }

    if (!transactionCustomerId) {
      throw new Error("No shopifyCustomerId found in transactions data");
    }

    // Use customer ID from transactions API (already in numeric format)
    const finalCustomerId = transactionCustomerId;

    // Step 3: Get order details via f etch order API
    console.log("🔍 Step 3: Getting order details...");

    const orderResponse = await fetch(
      `${process.env.BASE_URL || 'https://maryland-tongue-rope-iii.trycloudflare.com'}/api/rewardify/order/${orderId}`
    );

    if (!orderResponse.ok) {
      throw new Error(`Failed to fetch order details: ${orderResponse.status}`);
    }

    const orderData = await orderResponse.json();
    const order = orderData.orderData;

    // Step 4: Use store credit amount from transactions API (more accurate than Shopify discount applications)
    const totalDiscountAmount = storeCreditUsedAmount;
    console.log(`💸 Store credit amount to be used for calculations: ₹${totalDiscountAmount}`);

    // Step 5: Get price of each product and calculate total order subtotal
    let totalOrderSubtotal = 0;
    const productPrices = {};

    for (const lineItemEdge of order.lineItems.edges) {
      const lineItem = lineItemEdge.node;
      const productPrice = parseFloat(lineItem.variant.price) * lineItem.quantity;
      totalOrderSubtotal += productPrice;

      // Store product prices for proportion calculation
      productPrices[lineItem.variant.id] = {
        unitPrice: parseFloat(lineItem.variant.price),
        quantity: lineItem.quantity,
        totalPrice: productPrice,
        title: lineItem.title
      };
    }

    console.log(`🛒 Total order subtotal: ₹${totalOrderSubtotal}`);
    console.log(`📊 Product prices:`, productPrices);

    // Step 6: Calculate refund amount based on gateway type
    let totalRefundAmount = 0;
    const refundDetails = [];

    if (isCOD) {
      console.log("💰 COD Gateway: Refunding full amount + store credit used");

      // For COD: Refund full amount from webhook + store credit used
      for (const refundedItem of refundedLineItems) {
        const { quantity, subtotal } = refundedItem;

        // Find matching product
        let matchingProduct = null;
        let bestMatch = null;
        let smallestDifference = Infinity;

        for (const [variantId, product] of Object.entries(productPrices)) {
          const difference = Math.abs(product.totalPrice - subtotal);
          if (difference < smallestDifference) {
            smallestDifference = difference;
            bestMatch = [variantId, product];
          }
        }

        if (bestMatch && smallestDifference < 50) { // Allow for tax differences
          matchingProduct = bestMatch;
        }

        if (!matchingProduct) {
          console.log(`⚠️ Could not find matching product for refunded item with subtotal: ₹${subtotal}`);
          // For COD, still add the refund amount even if we can't match the product
          totalRefundAmount += subtotal;

          refundDetails.push({
            productTitle: "Unknown Product",
            variantId: "unknown",
            refundedAmount: subtotal.toFixed(2),
            refundType: "Full Amount (COD)"
          });
          continue;
        }

        const [variantId, productInfo] = matchingProduct;

        // For COD: Full refund amount + proportional store credit
        let refundAmount = subtotal; // Full refund amount

        // Add proportional store credit if any was used
        if (storeCreditUsedAmount > 0) {
          const productProportion = productInfo.totalPrice / totalOrderSubtotal;
          const productStoreCreditAmount = storeCreditUsedAmount * productProportion;
          const refundProportion = quantity / productInfo.quantity;
          const storeCreditRefund = productStoreCreditAmount * refundProportion;

          refundAmount += storeCreditRefund;

          refundDetails.push({
            productTitle: productInfo.title,
            variantId,
            originalPrice: productInfo.totalPrice,
            refundedQuantity: quantity,
            totalQuantity: productInfo.quantity,
            fullRefundAmount: subtotal.toFixed(2),
            storeCreditRefund: storeCreditRefund.toFixed(2),
            totalRefundAmount: refundAmount.toFixed(2),
            refundType: "Full Amount + Store Credit (COD)"
          });
        } else {
          refundDetails.push({
            productTitle: productInfo.title,
            variantId,
            refundedAmount: refundAmount.toFixed(2),
            refundType: "Full Amount (COD - No Store Credit)"
          });
        }

        totalRefundAmount += refundAmount;
      }
    } else {
      console.log("💳 Non-COD Gateway: Refunding only store credit used");

      // For non-COD: Only refund store credit used
      if (!transactionsData.foundDiscountRedemption || storeCreditUsedAmount === 0) {
        console.log("ℹ️ No store credit was used in this order, skipping refund");
        return {
          success: true,
          message: "No store credit refund needed - order didn't use store credit",
          refundAmount: 0,
          shouldCredit: false
        };
      }

      console.log("✅ Store credit was used in this order - proceeding with store credit refund only");

      // Calculate proportional store credit refund for each item
      for (const refundedItem of refundedLineItems) {
        const { quantity, line_item } = refundedItem;

        // Get variant_id from line_item in webhook
        const webhookVariantId = line_item?.variant_id;

        if (!webhookVariantId) {
          console.log(`⚠️ No variant_id found in refunded item:`, refundedItem);
          continue;
        }

        // Convert numeric variant_id to GraphQL format to match productPrices keys
        const graphqlVariantId = `gid://shopify/ProductVariant/${webhookVariantId}`;

        console.log(`🔍 Looking for variant: ${graphqlVariantId}`);

        // Find matching product by variant_id
        const productInfo = productPrices[graphqlVariantId];

        if (!productInfo) {
          console.log(`⚠️ Could not find product info for variant_id: ${graphqlVariantId}`);
          console.log(`Available variants:`, Object.keys(productPrices));
          continue;
        }

        console.log(`✅ Found matching product: ${productInfo.title} (₹${productInfo.totalPrice})`);

        const variantId = graphqlVariantId;

        // Calculate proportion of store credit for this product
        const productProportion = productInfo.totalPrice / totalOrderSubtotal;
        const productStoreCreditAmount = storeCreditUsedAmount * productProportion;

        // Calculate refund proportion based on quantity refunded
        const refundProportion = quantity / productInfo.quantity;
        const refundStoreCreditAmount = productStoreCreditAmount * refundProportion;

        totalRefundAmount += refundStoreCreditAmount;

        refundDetails.push({
          productTitle: productInfo.title,
          variantId,
          originalPrice: productInfo.totalPrice,
          refundedQuantity: quantity,
          totalQuantity: productInfo.quantity,
          productProportion: productProportion.toFixed(4),
          productStoreCreditAmount: productStoreCreditAmount.toFixed(2),
          refundProportion: refundProportion.toFixed(4),
          refundStoreCreditAmount: refundStoreCreditAmount.toFixed(2),
          refundType: "Store Credit Only (Non-COD)"
        });
      }
    }

    console.log(`💰 Total refund amount: ₹${totalRefundAmount.toFixed(2)}`);
    console.log(`📋 Refund details:`, refundDetails);

    // Return calculation results for crediting
    return {
      success: true,
      message: "Proportional calculation completed",
      refundAmount: totalRefundAmount.toFixed(2),
      storeCreditUsedAmount: storeCreditUsedAmount.toFixed(2),
      foundDiscountRedemption: transactionsData.foundDiscountRedemption,
      refundDetails,
      shouldCredit: totalRefundAmount > 0,
      gateway,
      isCOD,
      customerData: {
        customerId: finalCustomerId,
        customerEmail,
        orderId,
        orderName: order.name
      }
    };

  } catch (error) {
    console.error("❌ Error processing proportional store credit calculation:", error);
    throw error;
  }
};

export const webhookHandler = async (req, res) => {
  try {
    const webhookData = req.body;
    console.log("✅ Refund webhook received");
    console.log("Webhook data:", webhookData);

    // Extract order ID from webhook
    const orderId = webhookData.order_id;

    if (!orderId) {
      console.log("⚠️ No order ID found in webhook, skipping store credit refund");
      return res.status(200).send("Refund webhook processed - no order ID");
    }

    // 🔁 Process proportional store credit refund
    try {
      console.log("💳 Processing proportional store credit refund...");

      // Process proportional store credit refund with webhook data
      const refundResult = await processProportionalStoreCredit(webhookData);

      if (refundResult.shouldCredit) {
        // Get shopifyCustomerId from the refund result (extracted from transactions API)
        const shopifyCustomerId = refundResult.customerData.customerId;

        console.log(`💳 Using customer ID from transactions API: ${shopifyCustomerId}`);

        // Call the credit function to actually credit the customer
        const creditResponse = await fetch(
          `${process.env.BASE_URL || 'https://maryland-tongue-rope-iii.trycloudflare.com'}/api/rewardify/customer/${shopifyCustomerId}`,
          {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
              webhookData: webhookData
            })
          }
        );

        if (creditResponse.ok) {
          const creditResult = await creditResponse.json();
          console.log("✅ Store credit refund processing completed:", creditResult);
          res.status(200).send("Refund webhook processed with store credit refund");
        } else {
          console.error("❌ Failed to credit customer:", creditResponse.status);
          res.status(200).send("Refund webhook processed - credit failed");
        }
      } else {
        console.log("ℹ️ No store credit refund needed");
        res.status(200).send("Refund webhook processed - no store credit refund needed");
      }

    } catch (storeCreditError) {
      console.error("❌ Error processing store credit refund:", storeCreditError);
      // Don't fail the webhook if store credit processing fails
      res.status(200).send("Refund webhook processed - store credit refund failed");
    }

  } catch (err) {
    console.error("❌ Error in refund webhook:", err.message);
    res.status(500).send("Internal error");
  }
};




