import { creditCustomerInRewardify, getRewardifyOrders } from "../services/rewardifyService.js";
import { rewardifyClient } from "../utils/apiClient.js";
import { createRewardifyHeaders } from "../utils/storeCreditHelpers.js";
import { getATFromSQL } from "../middlewares/helpers.js";
import shopify from "../shopify.js";

// GraphQL query to fetch Shopify order details
const GET_ORDER_QUERY = `
  query getOrder($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      email
      phone
      createdAt
      updatedAt
      processedAt
      cancelledAt
      cancelReason
      confirmed
      fulfillmentStatus
      financialStatus
      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      customer {
        id
        firstName
        lastName
        email
        phone
      }
      shippingAddress {
        firstName
        lastName
        address1
        address2
        city
        province
        country
        zip
        phone
      }
      billingAddress {
        firstName
        lastName
        address1
        address2
        city
        province
        country
        zip
        phone
      }
      lineItems(first: 50) {
        edges {
          node {
            id
            title
            quantity
            variant {
              id
              title
              sku
              price
            }
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }
      transactions(first: 10) {
        edges {
          node {
            id
            kind
            status
            gateway
            amountSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            createdAt
          }
        }
      }
      discountApplications(first: 10) {
        edges {
          node {
            ... on DiscountCodeApplication {
              code
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
            ... on ManualDiscountApplication {
              title
              description
              value {
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
                ... on PricingPercentageValue {
                  percentage
                }
              }
            }
          }
        }
      }
    }
  }
`;


export const fetchRewardifyTransactions = async (req, res) => {
  const { orderId } = req.params;
  const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

  if (!orderId) {
    return res.status(400).json({ message: "Missing orderId in URL params" });
  }

  if (!token) {
    return res.status(401).json({ message: "Missing Authorization token in headers" });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    console.log("Raw response from getRewardifyOrders:", response);
    console.log("Response type:", typeof response);
    console.log("Is Array:", Array.isArray(response));

    // The apiClient already returns response.data, so response IS the data
    const transactions = Array.isArray(response) ? response : [];

    console.log("Final transactions:", transactions);

    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    res.status(200).json({
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    console.error("Error in fetchRewardifyTransactions:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to fetch Rewardify transactions" });
  }
};

export const creditStoreCreditToCustomer = async (req, res) => {
  try {
    const shopifyCustomerId = req.params.shopifyCustomerId;
    const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

    
    if (!token) {
      return res.status(401).json({ message: "Missing Authorization token in headers" });
    }

    const {
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    } = req.body;

    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    res.status(200).json({ transaction });
  } catch (error) {
    console.error("Error in creditStoreCreditToCustomer:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to credit customer" });
  }
};

// Simple Shopify order fetch function for testing
export const fetchRewardifyOrder = async (req, res) => {
  const { orderId } = req.params;

  if (!orderId) {
    return res.status(400).json({
      message: "Missing orderId in URL params",
      example: "GET /api/rewardify/order/12345"
    });
  }

  try {
    console.log(`🔍 Fetching Shopify order: ${orderId}`);

    // Get shop and access token
    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        message: "Authentication failed",
        error: "Access token not found for the specified shop"
      });
    }

    // Create Shopify GraphQL client
    const client = new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT }
    });

    // Convert orderId to Shopify GID format if it's just a number
    const shopifyOrderId = orderId.startsWith('gid://')
      ? orderId
      : `gid://shopify/Order/${orderId}`;

    console.log(`� Querying Shopify order with ID: ${shopifyOrderId}`);

    // Fetch order from Shopify
    const { data } = await client.request(GET_ORDER_QUERY, {
      variables: { orderId: shopifyOrderId }
    });

    if (!data.order) {
      return res.status(404).json({
        message: "Order not found",
        orderId,
        shopifyOrderId
      });
    }

    console.log("✅ Shopify order fetch successful");

    res.status(200).json({
      message: "Shopify order fetched successfully",
      orderId,
      shopifyOrderId,
      orderData: data.order,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("❌ Error fetching Shopify order:", error?.response?.data || error.message);

    const statusCode = error?.response?.status || 500;
    const errorMessage = error?.response?.data?.message ||
                        error?.body?.errors?.graphQLErrors?.[0]?.message ||
                        error.message ||
                        "Failed to fetch order";

    res.status(statusCode).json({
      message: "Failed to fetch Shopify order",
      error: errorMessage,
      orderId,
      statusCode
    });
  }
};
