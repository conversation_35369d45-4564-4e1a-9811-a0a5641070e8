import { creditCustomerInRewardify, getRewardifyOrders } from "../services/rewardifyService.js";


export const fetchRewardifyTransactions = async (req, res) => {
  const { orderId } = req.params;
  const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

  if (!orderId) {
    return res.status(400).json({ message: "Missing orderId in URL params" });
  }

  if (!token) {
    return res.status(401).json({ message: "Missing Authorization token in headers" });
  }

  try {
    const response = await getRewardifyOrders(token, orderId);
    console.log("Raw response from getRewardifyOrders:", response);
    console.log("Response type:", typeof response);
    console.log("Is Array:", Array.isArray(response));

    // The apiClient already returns response.data, so response IS the data
    const transactions = Array.isArray(response) ? response : [];

    console.log("Final transactions:", transactions);

    const foundDiscountRedemption = transactions.some(
      (tx) => tx.transactionType === "Discount Redemption"
    );

    res.status(200).json({
      orderId,
      foundDiscountRedemption,
      totalTransactions: transactions.length,
      transactions,
    });
  } catch (error) {
    console.error("Error in fetchRewardifyTransactions:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to fetch Rewardify transactions" });
  }
};

export const creditStoreCreditToCustomer = async (req, res) => {
  try {
    const shopifyCustomerId = req.params.shopifyCustomerId;
    const token = req.headers['authorization']?.replace(/^Bearer\s+/i, '').trim();

    
    if (!token) {
      return res.status(401).json({ message: "Missing Authorization token in headers" });
    }

    const {
      email,
      amount,
      memo,
      expiresAt,
      sendEmail,
      emailNote
    } = req.body;

    const response = await creditCustomerInRewardify({
      token,
      shopifyCustomerId,
      customerEmail: email,
      amount,
      itemTitle: memo, // we’ll use this just for logging
      orderId: memo?.match(/#(\d+)/)?.[1], // extract orderId from memo if needed
    });

    const transaction = response.data?.transaction || {
      transactionType: "API Manual",
      amount: parseFloat(amount).toFixed(4),
      amountCurrency: "INR"
    };

    res.status(200).json({ transaction });
  } catch (error) {
    console.error("Error in creditStoreCreditToCustomer:", error?.response?.data || error.message);
    res.status(500).json({ message: "Failed to credit customer" });
  }
};
