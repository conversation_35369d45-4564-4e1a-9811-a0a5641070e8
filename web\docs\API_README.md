# MiniKlub Store Credit App API Documentation

## Overview

The MiniKlub Store Credit App provides a comprehensive API for managing store credits, gift cards, and newsletter subscriptions. The API integrates with QwikCilver for gift card processing and Rewardify for store credit management.

## Base URL

- **Development**: `http://localhost:3000`
- **Production**: `https://your-production-domain.com`

## API Documentation

Interactive API documentation is available at:
- **Swagger UI**: `http://localhost:3000/api-docs`

## Authentication

### QwikCilver Authentication
1. Call `POST /api/store-credit/auth` with a transaction ID
2. Use the returned `authToken` in subsequent QwikCilver API calls
3. Include the token in the `Authorization` header as `Bearer {token}`

### Rewardify Authentication
1. Call `POST /api/store-credit/rewardify-auth` to get an access token
2. Use the returned `access_token` in Rewardify API calls
3. Include the token in the `Authorization` header as `Bearer {token}`

## API Endpoints

### Store Credit Management

#### QwikCilver Gift Cards

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/store-credit/auth` | Authenticate with QwikCilver |
| POST | `/api/store-credit/balance` | Check gift card balance |
| POST | `/api/store-credit/redeem-giftcard` | Redeem gift card amount |

#### Rewardify Store Credit

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/store-credit/rewardify-auth` | Authenticate with Rewardify |
| PUT | `/api/store-credit/rewardify-credit/{customerId}` | Credit customer account |

### Newsletter Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/newsletter/subscribe` | Subscribe/unsubscribe from newsletter |

## Quick Start Examples

### 1. Check Gift Card Balance

```bash
# Step 1: Authenticate with QwikCilver
curl -X POST http://localhost:3000/api/store-credit/auth \
  -H "Content-Type: application/json" \
  -d '{"transactionId": "*********"}'

# Step 2: Check balance (use authToken from step 1)
curl -X POST http://localhost:3000/api/store-credit/balance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -H "TransactionId: *********" \
  -H "DateAtClient: 2025-06-24T10:57:37.000Z" \
  -d '{
    "Cards": [{
      "CardNumber": "****************",
      "CardPIN": "143048"
    }]
  }'
```

### 2. Redeem Gift Card

```bash
# Redeem amount from gift card
curl -X POST http://localhost:3000/api/store-credit/redeem-giftcard \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -H "TransactionId: *********" \
  -H "DateAtClient: 2025-06-24T10:57:37.000Z" \
  -d '{
    "Cards": [{
      "CardNumber": "****************",
      "CardPin": "143048",
      "Amount": "100",
      "InvoiceAmount": "500"
    }]
  }'
```

### 3. Credit Rewardify Account

```bash
# Step 1: Get Rewardify token
curl -X POST http://localhost:3000/api/store-credit/rewardify-auth

# Step 2: Credit customer account (use access_token from step 1)
curl -X PUT http://localhost:3000/api/store-credit/rewardify-credit/************* \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_REWARDIFY_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "amount": 200,
    "memo": "Gift card redemption credit",
    "sendEmail": true,
    "emailNote": "Your store credit has been added"
  }'
```

### 4. Newsletter Subscription

```bash
# Subscribe to newsletter
curl -X POST http://localhost:3000/api/newsletter/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "action": "subscribe"
  }'

# Unsubscribe from newsletter
curl -X POST http://localhost:3000/api/newsletter/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "action": "unsubscribe"
  }'
```

## Response Format

All API responses follow a standardized format:

### Success Response
```json
{
  "responseCode": 0,
  "status": "success",
  "message": "Operation completed successfully",
  "data": {
    // Response data varies by endpoint
  }
}
```

### Error Response
```json
{
  "responseCode": 1,
  "status": "error",
  "statusCode": 400,
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "stack": "Error stack trace (development only)"
}
```

## Error Codes

| HTTP Status | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing authentication |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server or external API error |

## Rate Limiting

- **QwikCilver**: Follow QwikCilver API rate limits
- **Rewardify**: Standard OAuth2 rate limits
- **Shopify**: Subject to Shopify API rate limits

## Support

For API support and questions:
- **Email**: <EMAIL>
- **Documentation**: Visit `/api-docs` for interactive documentation
- **Issues**: Report issues through your development team

## Changelog

### Version 1.0.0
- Initial API release
- QwikCilver gift card integration
- Rewardify store credit integration
- Newsletter subscription management
- Comprehensive Swagger documentation
